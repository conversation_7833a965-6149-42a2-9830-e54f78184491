# MSEC简化自动签到脚本

这是一个简化版本的MSEC（绿盟科技安全云）自动签到脚本，去除了复杂的数据库管理、加密等功能，只保留核心的网络请求部分。

## 功能特点

✅ **核心功能完整**
- 获取验证码
- 用户登录
- 获取用户信息
- 获取积分信息
- 获取用户角色
- 获取签到历史
- 执行签到

✅ **简洁高效**
- 代码简洁，约300行
- 无复杂依赖
- 易于理解和修改

✅ **智能重试**
- 验证码错误自动重试
- 网络请求失败重试
- 友好的错误提示

✅ **时间显示优化**
- 角色过期时间显示具体日期（YYYY-MM-DD）
- 签到记录显示详细时间（YYYY-MM-DD HH:MM:SS (周X)）
- 自动识别毫秒级和秒级时间戳
- 支持中文星期显示

✅ **Token本地存储**
- 安全的文件加密存储（基于用户密码）
- 自动Token过期检测
- 多用户独立存储
- 免重复登录体验

## 依赖要求

```bash
pip install requests
```

## 使用方法

### 1. 直接运行
```bash
python msec_simple_checkin.py
```

然后按提示输入用户名和密码。

### 2. 编程调用
```python
from msec_simple_checkin import MSECSimpleClient

# 创建客户端（默认启用Token存储）
client = MSECSimpleClient("your_username", "your_password")

# 运行完整流程
success = client.run_full_process()

# 或者单独调用各个功能
if client.user_login():  # 自动检查已保存的Token
    client.get_user_info()
    client.get_points()
    client.get_roles()
    client.checkin()

# 禁用Token存储
client = MSECSimpleClient("username", "password", enable_token_storage=False)
```

## 脚本结构

```
msec_simple_checkin.py
├── get_week_checkin_params()    # 获取本周签到参数
└── MSECSimpleClient            # 主客户端类
    ├── __init__()              # 初始化
    ├── _make_request()         # 通用HTTP请求方法
    ├── get_captcha()           # 获取验证码
    ├── login()                 # 用户登录
    ├── get_user_info()         # 获取用户信息
    ├── get_points()            # 获取积分信息
    ├── get_roles()             # 获取角色信息
    ├── get_checkin_history()   # 获取签到历史
    ├── checkin()               # 执行签到
    ├── user_login()            # 用户登录（支持重试）
    ├── _load_saved_token()     # 加载保存的Token
    ├── _save_token()           # 保存Token到本地
    ├── is_logged_in()          # 检查登录状态
    ├── get_token_info()        # 获取Token信息
    ├── logout()                # 登出并清除Token
    └── run_full_process()      # 运行完整流程
```

## API端点

脚本使用以下MSEC API端点：

| 功能 | 端点 | 方法 |
|------|------|------|
| 获取验证码 | `/backend_api/account/captcha` | POST |
| 用户登录 | `/backend_api/account/login` | POST |
| 获取用户信息 | `/backend_api/account/info` | POST |
| 获取积分 | `/backend_api/point/common/get` | POST |
| 获取角色 | `/backend_api/rbac/role/self/list` | POST |
| 签到历史 | `/backend_api/checkin/history` | POST |
| 执行签到 | `/backend_api/checkin/checkin` | POST |

## Token本地存储

### 🔐 安全机制
- **加密算法**：PBKDF2 + Fernet对称加密
- **密钥派生**：基于用户密码生成加密密钥
- **盐值保护**：每次保存使用随机盐值
- **文件结构**：`[16字节盐值] + [加密数据]`

### 📁 存储位置
- **文件命名**：`msec_token_{用户名}.dat`
- **存储内容**：用户名、Token、保存时间、过期时间
- **多用户支持**：每个用户独立的加密文件

### 🔧 使用方式

#### 启用Token存储（默认）：
```python
client = MSECSimpleClient("username", "password")
# 自动加载已保存的Token，无需重复登录
```

#### 禁用Token存储：
```python
client = MSECSimpleClient("username", "password", enable_token_storage=False)
# 每次都需要重新登录
```

#### Token管理：
```python
# 检查登录状态
is_logged_in = client.is_logged_in()

# 获取Token信息
token_info = client.get_token_info()

# 手动登出（清除Token）
client.logout()
```

### ⚠️ 注意事项
- Token文件使用用户密码加密，忘记密码无法恢复
- 修改密码后需要重新登录生成新Token
- Token文件损坏会自动重新登录
- 建议定期清理过期的Token文件

## 快速启动

使用交互式菜单：

```bash
python quick_start.py
```

功能包括：
- 完整签到流程
- 单项功能测试
- Token管理
- 用户友好界面

## 输出示例

```
🚀 开始MSEC自动签到流程
==================================================
🔍 正在获取验证码...
🔍 验证码图片: data:image/png;base64,iVBORw0KGgo...
📋 验证码ID: sF90MMOErknD9JYkVqth
🔤 请输入验证码: 1234
🔐 正在登录...
✅ 登录成功!
👤 正在获取用户信息...
👤 用户: testuser (ID: 12345)
💰 正在获取积分信息...
💰 积分: 150/1000
🎭 正在获取角色信息...
🎭 角色: 普通用户(永久), VIP用户(至2025-12-31)
📅 正在获取签到历史 (从 2025-07-07 开始，5 天)...
📅 本周签到记录: 4次
📅 签到时间明细:
   • 2025-07-07 09:15:30 (周一)
   • 2025-07-08 08:45:12 (周二)
   • 2025-07-09 10:20:45 (周三)
   • 2025-07-10 09:30:18 (周四)
📝 正在执行签到...
✅ 签到成功！
==================================================
🎉 签到流程完成！
```

## 错误处理

脚本包含完善的错误处理：

- **网络错误**：自动重试，超时处理
- **验证码错误**：支持多次重试
- **登录失败**：详细错误信息
- **API错误**：状态码检查和错误提示

## 注意事项

1. **网络环境**：确保能够访问 `https://msec.nsfocus.com`
2. **验证码**：需要手动输入验证码，无法自动识别
3. **频率限制**：避免频繁请求，遵守网站使用规则
4. **账号安全**：妥善保管账号密码，不要在不安全的环境中使用

## 与原版本的区别

| 功能 | 原版本 | 简化版本 |
|------|--------|----------|
| 代码行数 | 2400+ | ~300 |
| 数据库支持 | ✅ | ❌ |
| 加密存储 | ✅ | ❌ |
| 多用户管理 | ✅ | ❌ |
| 配置文件 | ✅ | ❌ |
| 核心签到功能 | ✅ | ✅ |
| 网络请求 | ✅ | ✅ |
| 错误处理 | ✅ | ✅ |

## 许可证

本脚本仅供学习和个人使用，请遵守相关网站的使用条款。
