#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSEC简化自动签到脚本
功能：最简单的MSEC平台自动签到，只保留核心网络请求
"""

import requests
import json
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any
from simple_token_storage import SimpleTokenStorage


def get_week_checkin_params() -> Tuple[str, int]:
    """获取本周签到历史查询参数"""
    today = datetime.now()
    weekday = today.weekday()
    monday = today - timedelta(days=weekday)
    start_date = monday.strftime('%Y-%m-%d')
    days = weekday + 1
    return start_date, days


class MSECSimpleClient:
    """MSEC简化客户端"""
    
    def __init__(self, username: str, password: str, enable_token_storage: bool = True):
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.jwt_token = None
        self.enable_token_storage = enable_token_storage

        # 基础配置
        self.base_url = "https://msec.nsfocus.com"
        self.timeout = 30

        # Token存储
        if self.enable_token_storage:
            self.token_storage = SimpleTokenStorage(f"msec_token_{username}.dat")
            # 尝试加载已保存的Token
            self._load_saved_token()

        # 设置请求头
        self.session.headers.update({
            'Host': 'msec.nsfocus.com',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Origin': 'https://msec.nsfocus.com',
            'Referer': 'https://msec.nsfocus.com/auth/login',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        })

    def _load_saved_token(self) -> bool:
        """加载已保存的Token"""
        if not self.enable_token_storage:
            return False

        try:
            token = self.token_storage.load_token(self.username, self.password)
            if token:
                self.jwt_token = token
                self.session.headers['Authorization'] = token
                print("✅ 已加载保存的Token")
                return True
        except Exception as e:
            print(f"⚠️ 加载Token失败: {e}")

        return False

    def _save_token(self, token: str, expire_time: Optional[int] = None) -> bool:
        """保存Token到本地"""
        if not self.enable_token_storage:
            return False

        try:
            return self.token_storage.save_token(
                self.username,
                token,
                self.password,
                expire_time
            )
        except Exception as e:
            print(f"⚠️ 保存Token失败: {e}")
            return False

    def is_logged_in(self) -> bool:
        """检查是否已登录（有有效Token）"""
        if not self.jwt_token:
            return False

        if self.enable_token_storage:
            return self.token_storage.is_token_valid(self.username, self.password)

        return True

    def get_token_info(self) -> Optional[Dict[str, Any]]:
        """获取Token信息"""
        if not self.enable_token_storage:
            return None

        return self.token_storage.get_token_info(self.username, self.password)

    def logout(self) -> bool:
        """登出并清除Token"""
        self.jwt_token = None
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']

        if self.enable_token_storage:
            return self.token_storage.delete_token()

        return True

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Optional[requests.Response]:
        """发送HTTP请求"""
        url = self.base_url + endpoint
        
        try:
            if method.upper() == 'POST':
                response = self.session.post(url, json=data or {}, timeout=self.timeout)
            else:
                response = self.session.get(url, timeout=self.timeout)
            
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    def get_captcha(self) -> Tuple[Optional[str], Optional[str]]:
        """获取验证码"""
        print("🔍 正在获取验证码...")
        response = self._make_request('POST', '/backend_api/account/captcha')
        
        if not response:
            return None, None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取验证码失败: {data}")
                return None, None
            
            captcha_data = data.get('data', {})
            captcha_id = captcha_data.get('id')
            captcha_image = captcha_data.get('captcha')
            
            if captcha_id and captcha_image:
                print(f"🔍 验证码图片: {captcha_image}")
                print(f"📋 验证码ID: {captcha_id}")
                return captcha_id, captcha_image
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析验证码响应失败: {e}")
        
        return None, None
    
    def login(self, captcha_id: str, captcha_answer: str) -> bool:
        """用户登录"""
        print("🔐 正在登录...")
        
        login_data = {
            "captcha_answer": captcha_answer,
            "captcha_id": captcha_id,
            "password": self.password,
            "username": self.username
        }
        
        response = self._make_request('POST', '/backend_api/account/login', login_data)
        
        if not response:
            return False
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 登录失败: {data}")
                return False
            
            token = data.get('data', {}).get('token')
            if token:
                self.jwt_token = token
                self.session.headers['Authorization'] = token

                # 尝试解析Token过期时间并保存
                expire_time = None
                try:
                    import jwt as jwt_lib
                    decoded = jwt_lib.decode(token, options={"verify_signature": False})
                    expire_time = decoded.get('exp')
                except:
                    # 如果解析失败，设置默认过期时间（24小时后）
                    import time
                    expire_time = int(time.time()) + 24 * 3600

                # 保存Token到本地
                self._save_token(token, expire_time)

                print("✅ 登录成功!")
                return True
            else:
                print("❌ 未获取到token")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ 解析登录响应失败: {e}")
            return False
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        if not self.jwt_token:
            print("❌ 未登录，无法获取用户信息")
            return None
        
        print("👤 正在获取用户信息...")
        self.session.headers['Referer'] = 'https://msec.nsfocus.com/'
        
        response = self._make_request('POST', '/backend_api/account/info')
        
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取用户信息失败: {data}")
                return None
            
            user_info = data.get('data', {}).get('user', {})
            if user_info:
                print(f"👤 用户: {user_info.get('username')} (ID: {user_info.get('id')})")
                return user_info
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析用户信息响应失败: {e}")
        
        return None
    
    def get_points(self) -> Optional[Dict[str, Any]]:
        """获取积分信息"""
        if not self.jwt_token:
            print("❌ 未登录，无法获取积分信息")
            return None
        
        print("💰 正在获取积分信息...")
        response = self._make_request('POST', '/backend_api/point/common/get')
        
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取积分信息失败: {data}")
                return None
            
            points_data = data.get('data', {})
            if 'accrued' in points_data and 'total' in points_data:
                print(f"💰 积分: {points_data.get('accrued')}/{points_data.get('total')}")
                return points_data
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析积分信息响应失败: {e}")
        
        return None
    
    def get_roles(self) -> Optional[Dict[str, Any]]:
        """获取用户角色信息"""
        if not self.jwt_token:
            print("❌ 未登录，无法获取角色信息")
            return None
        
        print("🎭 正在获取角色信息...")
        role_data = {"limit": 100, "offset": 0}
        
        response = self._make_request('POST', '/backend_api/rbac/role/self/list', role_data)
        
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取角色信息失败: {data}")
                return None
            
            roles_data = data.get('data', {})
            role_list = roles_data.get('list', [])
            
            if role_list:
                role_names: list[str] = []
                for role in role_list:
                    role_name = role.get('role_name', '未知角色')
                    # 处理Unicode编码
                    if isinstance(role_name, str) and '\\u' in role_name:
                        try:
                            role_name = role_name.encode().decode('unicode_escape')
                        except:
                            pass
                    
                    expire_time = role.get('expire_time', 0)
                    if expire_time == 0:
                        role_names.append(f"{role_name}(永久)")
                    else:
                        # 将时间戳转换为年月日格式
                        try:
                            from datetime import datetime
                            expire_date = datetime.fromtimestamp(expire_time).strftime('%Y-%m-%d')
                            role_names.append(f"{role_name}(至{expire_date})")
                        except:
                            role_names.append(f"{role_name}(限时)")
                
                print(f"🎭 角色: {', '.join(role_names)}")
            else:
                print("🎭 角色: 无")
            
            return roles_data
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析角色信息响应失败: {e}")
        
        return None
    
    def get_checkin_history(self, start_date: str, days: int = 5) -> Optional[Dict[str, Any]]:
        """获取签到历史"""
        if not self.jwt_token:
            print("❌ 未登录，无法获取签到历史")
            return None
        
        print(f"📅 正在获取签到历史 (从 {start_date} 开始，{days} 天)...")
        history_data: Dict[str, Any] = {"start_date": start_date, "days": days}
        
        response = self._make_request('POST', '/backend_api/checkin/history', history_data)
        
        if not response:
            return None
        
        try:
            data = response.json()
            if data.get('status') != 200:
                print(f"❌ 获取签到历史失败: {data}")
                return None
            
            history_data = data.get('data', {})
            records_list = history_data.get('records_list', {})
            total_records = records_list.get('total', 0)
            checkin_list = records_list.get('list', [])

            print(f"📅 本周签到记录: {total_records}次")

            # 显示具体的签到时间
            if checkin_list:
                print("📅 签到时间明细:")
                for record in checkin_list:
                    checkin_time = record.get('checkin_time', '')
                    if checkin_time:
                        try:
                            # 将时间戳转换为可读格式
                            from datetime import datetime
                            if isinstance(checkin_time, (int, float)):
                                # 检查是否为毫秒级时间戳（13位数字）
                                if checkin_time > 9999999999:  # 大于10位数字，可能是毫秒
                                    timestamp = checkin_time / 1000  # 转换为秒
                                else:
                                    timestamp = checkin_time

                                # 转换为datetime对象
                                dt = datetime.fromtimestamp(timestamp)

                                # 获取星期几（中文）
                                weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                                weekday_cn = weekdays[dt.weekday()]

                                # 格式化时间显示
                                checkin_date = dt.strftime('%Y-%m-%d %H:%M:%S')
                                print(f"   • {checkin_date} ({weekday_cn})")
                            else:
                                # 如果已经是字符串格式
                                checkin_date = str(checkin_time)
                                print(f"   • {checkin_date}")
                        except Exception as e:
                            print(f"   • {checkin_time} (时间格式错误)")

            return history_data
            
        except json.JSONDecodeError as e:
            print(f"❌ 解析签到历史响应失败: {e}")
        
        return None
    
    def checkin(self) -> bool:
        """执行签到"""
        if not self.jwt_token:
            print("❌ 未登录，无法签到")
            return False
        
        print("📝 正在执行签到...")
        response = self._make_request('POST', '/backend_api/checkin/checkin')
        
        if not response:
            return False
        
        try:
            data = response.json()
            status = data.get('status')
            
            if status == 200:
                print("✅ 签到成功！")
                return True
            elif status == 400:
                print("⚠️ 今日已签到")
                return True
            else:
                print(f"❌ 签到失败 (状态: {status})")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ 解析签到响应失败: {e}")
            return False
    
    def user_login(self, max_attempts: int = 3) -> bool:
        """用户登录（支持验证码重试）"""
        # 首先检查是否已有有效Token
        if self.is_logged_in():
            print("✅ 检测到有效Token，无需重新登录")
            return True

        for attempt in range(max_attempts):
            if attempt > 0:
                print(f"🔄 第{attempt + 1}次尝试...")
            
            # 获取验证码
            captcha_id, _ = self.get_captcha()
            if not captcha_id:
                print("❌ 验证码获取失败")
                continue
            
            # 用户输入验证码
            try:
                captcha_answer = input("🔤 请输入验证码: ").strip()
                if not captcha_answer:
                    print("⚠️ 验证码不能为空")
                    continue
                
                # 尝试登录
                if self.login(captcha_id, captcha_answer):
                    return True
                else:
                    if attempt < max_attempts - 1:
                        print(f"❌ 验证码错误，还有 {max_attempts - attempt - 1} 次机会")
                    
            except KeyboardInterrupt:
                print("\n用户取消了输入")
                return False
        
        print("❌ 登录失败，已达到最大尝试次数")
        return False
    
    def run_full_process(self) -> bool:
        """运行完整流程"""
        print("🚀 开始MSEC自动签到流程")
        print("=" * 50)
        
        # 1. 登录
        if not self.user_login():
            return False
        
        # 2. 获取用户信息
        self.get_user_info()
        
        # 3. 获取积分信息
        self.get_points()
        
        # 4. 获取角色信息
        self.get_roles()
        
        # 5. 获取签到历史
        start_date, days = get_week_checkin_params()
        self.get_checkin_history(start_date, days)
        
        # 6. 执行签到
        success = self.checkin()
        
        print("=" * 50)
        if success:
            print("🎉 签到流程完成！")
        else:
            print("❌ 签到流程失败")
        
        return success


def main():
    """主函数"""
    print("MSEC简化自动签到脚本")
    print("=" * 30)
    
    # 获取用户输入
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return
    
    # 创建客户端并运行
    client = MSECSimpleClient(username, password)
    client.run_full_process()


if __name__ == "__main__":
    main()
