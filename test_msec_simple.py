#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSEC简化脚本测试
测试脚本的基本功能和网络连接
"""

import sys
import requests
from msec_simple_checkin import MSECSimpleClient, get_week_checkin_params


def test_network_connectivity():
    """测试网络连接"""
    print("🌐 测试网络连接...")
    try:
        response = requests.get("https://msec.nsfocus.com", timeout=10)
        if response.status_code == 200:
            print("✅ 网络连接正常")
            return True
        else:
            print(f"⚠️ 网站响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络连接失败: {e}")
        return False


def test_week_params():
    """测试周参数计算"""
    print("📅 测试周参数计算...")
    try:
        start_date, days = get_week_checkin_params()
        print(f"✅ 本周开始日期: {start_date}, 今天是第{days}天")
        return True
    except Exception as e:
        print(f"❌ 周参数计算失败: {e}")
        return False


def test_client_initialization():
    """测试客户端初始化"""
    print("🔧 测试客户端初始化...")
    try:
        client = MSECSimpleClient("test_user", "test_pass")
        print("✅ 客户端初始化成功")
        print(f"   - 基础URL: {client.base_url}")
        print(f"   - 超时设置: {client.timeout}秒")
        print(f"   - 用户名: {client.username}")
        print(f"   - 请求头已设置: {'User-Agent' in client.session.headers}")
        return True
    except Exception as e:
        print(f"❌ 客户端初始化失败: {e}")
        return False


def test_captcha_endpoint():
    """测试验证码端点（不需要登录）"""
    print("🔍 测试验证码端点...")
    try:
        client = MSECSimpleClient("test_user", "test_pass")
        captcha_id, captcha_image = client.get_captcha()
        
        if captcha_id and captcha_image:
            print("✅ 验证码获取成功")
            print(f"   - 验证码ID长度: {len(captcha_id)}")
            print(f"   - 验证码图片长度: {len(captcha_image)}")
            return True
        else:
            print("⚠️ 验证码获取失败（可能是网络问题或服务器问题）")
            return False
    except Exception as e:
        print(f"❌ 验证码端点测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("MSEC简化脚本功能测试")
    print("=" * 40)
    
    tests = [
        ("网络连接", test_network_connectivity),
        ("周参数计算", test_week_params),
        ("客户端初始化", test_client_initialization),
        ("验证码端点", test_captcha_endpoint),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        print("-" * 20)
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
        
        print("-" * 20)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    print("=" * 40)
    
    if passed == total:
        print("🎉 所有测试通过！脚本基本功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查网络连接或脚本配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
