#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示时间显示功能
模拟真实的API响应数据来展示时间格式化效果
"""

import time
from datetime import datetime
from msec_simple_checkin import MSECSimpleClient


class MockMSECClient(MSECSimpleClient):
    """模拟MSEC客户端，用于演示时间显示功能"""
    
    def __init__(self):
        # 不调用父类初始化，避免网络请求
        self.username = "demo_user"
        self.password = "demo_pass"
        self.jwt_token = "mock_token"
    
    def get_roles(self):
        """模拟获取角色信息"""
        print("🎭 正在获取角色信息...")
        
        # 模拟角色数据
        mock_roles = {
            'list': [
                {
                    'role_name': '普通用户',
                    'expire_time': 0  # 永久
                },
                {
                    'role_name': 'VIP用户',
                    'expire_time': int(time.time()) + 86400 * 90  # 90天后过期
                },
                {
                    'role_name': '测试用户',
                    'expire_time': 1735689600  # 2025-01-01
                },
                {
                    'role_name': '高级用户',
                    'expire_time': int(time.time()) + 86400 * 365  # 1年后过期
                }
            ]
        }
        
        role_list = mock_roles.get('list', [])
        
        if role_list:
            role_names = []
            for role in role_list:
                role_name = role.get('role_name', '未知角色')
                expire_time = role.get('expire_time', 0)
                
                if expire_time == 0:
                    role_names.append(f"{role_name}(永久)")
                else:
                    try:
                        expire_date = datetime.fromtimestamp(expire_time).strftime('%Y-%m-%d')
                        role_names.append(f"{role_name}(至{expire_date})")
                    except:
                        role_names.append(f"{role_name}(限时)")
            
            print(f"🎭 角色: {', '.join(role_names)}")
        else:
            print("🎭 角色: 无")
        
        return mock_roles
    
    def get_checkin_history(self, start_date, days=5):
        """模拟获取签到历史"""
        print(f"📅 正在获取签到历史 (从 {start_date} 开始，{days} 天)...")
        
        # 模拟签到记录数据
        current_time = int(time.time())
        mock_history = {
            'records_list': {
                'total': 5,
                'list': [
                    {'checkin_time': current_time - 86400 * 4},  # 4天前
                    {'checkin_time': current_time - 86400 * 3},  # 3天前
                    {'checkin_time': current_time - 86400 * 2},  # 2天前
                    {'checkin_time': current_time - 86400 * 1},  # 1天前
                    {'checkin_time': current_time},  # 今天
                ]
            }
        }
        
        history_data = mock_history
        records_list = history_data.get('records_list', {})
        total_records = records_list.get('total', 0)
        checkin_list = records_list.get('list', [])
        
        print(f"📅 本周签到记录: {total_records}次")
        
        # 显示具体的签到时间
        if checkin_list:
            print("📅 签到时间明细:")
            for record in checkin_list:
                checkin_time = record.get('checkin_time', '')
                if checkin_time:
                    try:
                        if isinstance(checkin_time, (int, float)):
                            # 如果是时间戳
                            checkin_date = datetime.fromtimestamp(checkin_time).strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            # 如果已经是字符串格式
                            checkin_date = str(checkin_time)
                        print(f"   • {checkin_date}")
                    except:
                        print(f"   • {checkin_time}")
        
        return history_data


def demo_time_features():
    """演示时间显示功能"""
    print("MSEC时间显示功能演示")
    print("=" * 40)
    
    # 创建模拟客户端
    client = MockMSECClient()
    
    print("🚀 演示角色过期时间显示:")
    print("-" * 30)
    client.get_roles()
    
    print("\n🚀 演示签到时间明细显示:")
    print("-" * 30)
    from msec_simple_checkin import get_week_checkin_params
    start_date, days = get_week_checkin_params()
    client.get_checkin_history(start_date, days)
    
    print("\n" + "=" * 40)
    print("✅ 时间显示功能演示完成！")
    print("\n📋 功能说明:")
    print("• 角色过期时间：永久显示'(永久)'，限时显示'(至YYYY-MM-DD)'")
    print("• 签到时间：自动格式化为'YYYY-MM-DD HH:MM:SS'格式")
    print("• 时间处理：支持时间戳和字符串两种格式")


if __name__ == "__main__":
    demo_time_features()
