#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多用户功能演示脚本
展示如何使用环境变量配置多用户批量签到
"""

import os
from msec_simple_checkin import parse_env_users, get_yunma_token, run_multi_user_checkin


def demo_env_parsing():
    """演示环境变量解析功能"""
    print("🔍 环境变量解析演示")
    print("=" * 30)
    
    # 模拟设置环境变量
    test_cases = [
        {
            "MSEC_USER": "user1,user2,user3",
            "MSEC_PASS": "pass1,pass2,pass3",
            "YUNMA_TOKEN": "test_token_123"
        },
        {
            "MSEC_USER": "alice, bob, charlie",  # 带空格
            "MSEC_PASS": "pwd1, pwd2, pwd3",
            "YUNMA_TOKEN": ""
        },
        {
            "MSEC_USER": "single_user",
            "MSEC_PASS": "single_pass",
            "YUNMA_TOKEN": "yunma_token_456"
        },
        {
            "MSEC_USER": "user1,user2",  # 数量不匹配
            "MSEC_PASS": "pass1",
            "YUNMA_TOKEN": "token"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}:")
        
        # 设置环境变量
        for key, value in test_case.items():
            os.environ[key] = value
            print(f"   {key}={value}")
        
        # 解析用户配置
        users = parse_env_users()
        yunma_token = get_yunma_token()
        
        print(f"   解析结果:")
        print(f"   - 用户数量: {len(users)}")
        if users:
            for j, (username, password) in enumerate(users, 1):
                print(f"   - 用户{j}: {username} / {password}")
        print(f"   - 云码Token: {yunma_token or '未配置'}")
        
        # 清理环境变量
        for key in test_case.keys():
            if key in os.environ:
                del os.environ[key]


def demo_multi_user_simulation():
    """演示多用户签到模拟"""
    print("\n🚀 多用户签到模拟演示")
    print("=" * 30)
    
    # 模拟用户数据
    test_users = [
        ("demo_user1", "demo_pass1"),
        ("demo_user2", "demo_pass2"),
        ("demo_user3", "demo_pass3")
    ]
    
    print(f"👥 模拟用户: {len(test_users)} 个")
    for i, (username, _) in enumerate(test_users, 1):
        print(f"   {i}. {username}")
    
    print("\n⚠️ 注意: 这是模拟演示，不会进行真实的网络请求")
    print("实际使用时，请设置正确的环境变量:")
    print("export MSEC_USER='user1,user2,user3'")
    print("export MSEC_PASS='pass1,pass2,pass3'")
    print("export YUNMA_TOKEN='your_yunma_token'")


def demo_env_setup_guide():
    """演示环境变量设置指南"""
    print("\n📋 环境变量设置指南")
    print("=" * 30)
    
    print("🔧 Windows (PowerShell):")
    print('$env:MSEC_USER="user1,user2,user3"')
    print('$env:MSEC_PASS="pass1,pass2,pass3"')
    print('$env:YUNMA_TOKEN="your_yunma_token"')
    
    print("\n🔧 Windows (CMD):")
    print('set MSEC_USER=user1,user2,user3')
    print('set MSEC_PASS=pass1,pass2,pass3')
    print('set YUNMA_TOKEN=your_yunma_token')
    
    print("\n🔧 Linux/macOS (Bash):")
    print('export MSEC_USER="user1,user2,user3"')
    print('export MSEC_PASS="pass1,pass2,pass3"')
    print('export YUNMA_TOKEN="your_yunma_token"')
    
    print("\n🔧 Python脚本中设置:")
    print('import os')
    print('os.environ["MSEC_USER"] = "user1,user2,user3"')
    print('os.environ["MSEC_PASS"] = "pass1,pass2,pass3"')
    print('os.environ["YUNMA_TOKEN"] = "your_yunma_token"')
    
    print("\n📝 注意事项:")
    print("• 用户名和密码用逗号分隔")
    print("• 用户名数量必须与密码数量匹配")
    print("• 云码Token是可选的，用于自动验证码识别")
    print("• 建议将敏感信息存储在安全的地方")


def demo_usage_examples():
    """演示使用示例"""
    print("\n💡 使用示例")
    print("=" * 30)
    
    print("📋 示例1: 单用户配置")
    print('export MSEC_USER="alice"')
    print('export MSEC_PASS="alice_password"')
    print('export YUNMA_TOKEN="yunma_token_123"')
    print("python msec_simple_checkin.py")
    
    print("\n📋 示例2: 多用户配置")
    print('export MSEC_USER="alice,bob,charlie"')
    print('export MSEC_PASS="pass1,pass2,pass3"')
    print('export YUNMA_TOKEN="yunma_token_456"')
    print("python msec_simple_checkin.py")
    
    print("\n📋 示例3: 无验证码识别")
    print('export MSEC_USER="user1,user2"')
    print('export MSEC_PASS="pwd1,pwd2"')
    print("# 不设置YUNMA_TOKEN")
    print("python msec_simple_checkin.py")
    
    print("\n📋 示例4: 交互模式")
    print("# 不设置任何环境变量")
    print("python msec_simple_checkin.py")
    print("# 脚本将进入交互模式，要求手动输入用户信息")


def main():
    """主演示函数"""
    print("MSEC多用户功能演示")
    print("=" * 50)
    
    try:
        demo_env_parsing()
        demo_multi_user_simulation()
        demo_env_setup_guide()
        demo_usage_examples()
        
        print("\n" + "=" * 50)
        print("✅ 多用户功能演示完成！")
        
        print("\n🚀 快速开始:")
        print("1. 设置环境变量 MSEC_USER, MSEC_PASS, YUNMA_TOKEN")
        print("2. 运行 python msec_simple_checkin.py")
        print("3. 选择批量签到模式")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
