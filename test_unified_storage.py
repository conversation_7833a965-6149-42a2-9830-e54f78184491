#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一Token存储功能
"""

import os
import time
from msec_simple_checkin import MSECSimpleClient


def test_unified_storage():
    """测试统一存储功能"""
    print("统一Token存储测试")
    print("=" * 30)
    
    # 测试多个用户
    users = [
        ("alice", "password123"),
        ("bob", "secret456"),
        ("charlie", "mypass789")
    ]
    
    clients = []
    
    print("1. 创建多个客户端并模拟保存Token")
    for username, password in users:
        client = MSECSimpleClient(username, password, enable_token_storage=True)
        
        # 模拟保存Token
        mock_token = f"jwt.token.for.{username}"
        expire_time = int(time.time()) + 3600  # 1小时后过期
        
        success = client._save_token(mock_token, expire_time)
        print(f"   {username}: {'✅' if success else '❌'}")
        clients.append(client)
    
    print("\n2. 验证Token加载")
    for i, (username, password) in enumerate(users):
        client = MSECSimpleClient(username, password, enable_token_storage=True)
        is_logged_in = client.is_logged_in()
        print(f"   {username}: {'✅' if is_logged_in else '❌'}")
    
    print("\n3. 查看统一存储文件")
    storage_file = "msec_tokens.json"
    if os.path.exists(storage_file):
        file_size = os.path.getsize(storage_file)
        print(f"   文件: {storage_file}")
        print(f"   大小: {file_size} 字节")
        
        # 查看所有用户
        all_users = clients[0].token_storage.get_all_users()
        print(f"   用户: {all_users}")
    
    print("\n4. 测试Token信息获取")
    username, password = users[0]
    client = MSECSimpleClient(username, password, enable_token_storage=True)
    token_info = client.get_token_info()
    if token_info:
        print(f"   用户: {token_info['username']}")
        print(f"   剩余时间: {token_info['remaining_seconds']}秒")
        print(f"   是否有效: {token_info['is_valid']}")
    
    print("\n5. 测试单个用户登出")
    logout_success = clients[0].logout()
    print(f"   登出结果: {'✅' if logout_success else '❌'}")
    
    # 验证该用户Token已删除，其他用户Token仍存在
    remaining_users = clients[0].token_storage.get_all_users()
    print(f"   剩余用户: {remaining_users}")
    
    print("\n6. 清理测试数据")
    # 删除整个文件
    clients[1].token_storage.delete_token()
    print("   ✅ 测试数据已清理")
    
    print("\n✅ 统一存储测试完成")


def test_migration_compatibility():
    """测试迁移兼容性"""
    print("\n迁移兼容性测试")
    print("=" * 30)
    
    # 测试向后兼容性
    from simple_token_storage import SimpleTokenStorage, UnifiedTokenStorage
    
    print("1. 测试别名兼容性")
    storage1 = SimpleTokenStorage()
    storage2 = UnifiedTokenStorage()
    
    print(f"   SimpleTokenStorage类型: {type(storage1).__name__}")
    print(f"   UnifiedTokenStorage类型: {type(storage2).__name__}")
    print(f"   类型相同: {type(storage1) == type(storage2)}")
    
    print("\n✅ 兼容性测试完成")


def main():
    """主测试函数"""
    try:
        test_unified_storage()
        test_migration_compatibility()
        
        print("\n" + "=" * 40)
        print("🎉 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
