#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单Token存储模块
基于文件的加密存储，使用用户密码作为加密密钥
"""

import json
import base64
import time
import os
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class SimpleTokenStorage:
    """简单的Token加密存储类"""
    
    def __init__(self, storage_file: str = "msec_token.dat"):
        """
        初始化Token存储
        
        Args:
            storage_file: 存储文件路径
        """
        self.storage_file = storage_file
    
    def _generate_key(self, password: str, salt: bytes = None) -> tuple[bytes, bytes]:
        """
        基于密码生成加密密钥
        
        Args:
            password: 用户密码
            salt: 盐值，如果为None则生成新的
            
        Returns:
            (key, salt) 元组
        """
        if salt is None:
            salt = os.urandom(16)  # 生成16字节随机盐值
        
        # 使用PBKDF2派生密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,  # 迭代次数，增加破解难度
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt
    
    def save_token(self, username: str, token: str, password: str, 
                   expire_time: Optional[int] = None) -> bool:
        """
        保存Token到加密文件
        
        Args:
            username: 用户名
            token: JWT Token
            password: 用户密码（用于加密）
            expire_time: Token过期时间戳
            
        Returns:
            保存是否成功
        """
        try:
            # 生成加密密钥
            key, salt = self._generate_key(password)
            fernet = Fernet(key)
            
            # 准备要存储的数据
            data = {
                "username": username,
                "token": token,
                "save_time": int(time.time()),
                "expire_time": expire_time or (int(time.time()) + 24 * 3600)  # 默认24小时后过期
            }
            
            # 加密数据
            json_data = json.dumps(data, ensure_ascii=False)
            encrypted_data = fernet.encrypt(json_data.encode('utf-8'))
            
            # 写入文件：盐值(16字节) + 加密数据
            with open(self.storage_file, 'wb') as f:
                f.write(salt + encrypted_data)
            
            print(f"✅ Token已保存到 {self.storage_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存Token失败: {e}")
            return False
    
    def load_token(self, username: str, password: str) -> Optional[str]:
        """
        从加密文件加载Token
        
        Args:
            username: 用户名
            password: 用户密码（用于解密）
            
        Returns:
            Token字符串，失败返回None
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(self.storage_file):
                return None
            
            # 读取文件
            with open(self.storage_file, 'rb') as f:
                content = f.read()
            
            if len(content) < 16:
                return None
            
            # 分离盐值和加密数据
            salt = content[:16]
            encrypted_data = content[16:]
            
            # 重新生成密钥
            key, _ = self._generate_key(password, salt)
            fernet = Fernet(key)
            
            # 解密数据
            decrypted_data = fernet.decrypt(encrypted_data)
            data = json.loads(decrypted_data.decode('utf-8'))
            
            # 验证用户名
            if data.get("username") != username:
                print("❌ 用户名不匹配")
                return None
            
            # 检查Token是否过期
            expire_time = data.get("expire_time", 0)
            current_time = int(time.time())
            
            if current_time >= expire_time:
                print("⚠️ Token已过期")
                return None
            
            print("✅ Token加载成功")
            return data.get("token")
            
        except Exception as e:
            print(f"❌ 加载Token失败: {e}")
            return None
    
    def is_token_valid(self, username: str, password: str) -> bool:
        """
        检查Token是否有效（存在且未过期）
        
        Args:
            username: 用户名
            password: 用户密码
            
        Returns:
            Token是否有效
        """
        token = self.load_token(username, password)
        return token is not None
    
    def get_token_info(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """
        获取Token详细信息
        
        Args:
            username: 用户名
            password: 用户密码
            
        Returns:
            Token信息字典，失败返回None
        """
        try:
            if not os.path.exists(self.storage_file):
                return None
            
            with open(self.storage_file, 'rb') as f:
                content = f.read()
            
            if len(content) < 16:
                return None
            
            salt = content[:16]
            encrypted_data = content[16:]
            
            key, _ = self._generate_key(password, salt)
            fernet = Fernet(key)
            
            decrypted_data = fernet.decrypt(encrypted_data)
            data = json.loads(decrypted_data.decode('utf-8'))
            
            if data.get("username") != username:
                return None
            
            # 计算剩余有效时间
            expire_time = data.get("expire_time", 0)
            current_time = int(time.time())
            remaining_time = max(0, expire_time - current_time)
            
            return {
                "username": data.get("username"),
                "save_time": data.get("save_time"),
                "expire_time": data.get("expire_time"),
                "remaining_seconds": remaining_time,
                "is_valid": remaining_time > 0
            }
            
        except Exception as e:
            print(f"❌ 获取Token信息失败: {e}")
            return None
    
    def delete_token(self) -> bool:
        """
        删除Token文件
        
        Returns:
            删除是否成功
        """
        try:
            if os.path.exists(self.storage_file):
                os.remove(self.storage_file)
                print(f"✅ Token文件已删除: {self.storage_file}")
                return True
            else:
                print("⚠️ Token文件不存在")
                return True
        except Exception as e:
            print(f"❌ 删除Token文件失败: {e}")
            return False
    
    def file_exists(self) -> bool:
        """
        检查Token文件是否存在
        
        Returns:
            文件是否存在
        """
        return os.path.exists(self.storage_file)


def demo_token_storage():
    """演示Token存储功能"""
    print("Token存储功能演示")
    print("=" * 30)
    
    storage = SimpleTokenStorage("demo_token.dat")
    
    # 测试数据
    username = "test_user"
    password = "test_password_123"
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token_data"
    
    print("1. 保存Token...")
    success = storage.save_token(username, token, password)
    print(f"保存结果: {success}")
    
    print("\n2. 加载Token...")
    loaded_token = storage.load_token(username, password)
    print(f"加载结果: {loaded_token is not None}")
    print(f"Token匹配: {loaded_token == token}")
    
    print("\n3. 获取Token信息...")
    info = storage.get_token_info(username, password)
    if info:
        print(f"用户名: {info['username']}")
        print(f"保存时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info['save_time']))}")
        print(f"过期时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info['expire_time']))}")
        print(f"剩余时间: {info['remaining_seconds']}秒")
        print(f"是否有效: {info['is_valid']}")
    
    print("\n4. 测试错误密码...")
    wrong_token = storage.load_token(username, "wrong_password")
    print(f"错误密码结果: {wrong_token is None}")
    
    print("\n5. 清理测试文件...")
    storage.delete_token()
    
    print("\n✅ 演示完成")


if __name__ == "__main__":
    demo_token_storage()
