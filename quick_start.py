#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSEC快速启动脚本
提供更友好的用户界面和选项
"""

import sys
from msec_simple_checkin import MSECSimpleClient


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("           MSEC简化自动签到脚本")
    print("         绿盟科技安全云 - 快速签到")
    print("=" * 60)


def print_menu():
    """打印菜单"""
    print("\n📋 请选择操作:")
    print("1. 🚀 完整签到流程（推荐）")
    print("2. 🔐 仅登录测试")
    print("3. 👤 获取用户信息")
    print("4. 💰 查看积分信息")
    print("5. 🎭 查看角色信息")
    print("6. 📅 查看签到历史")
    print("7. 📝 仅执行签到")
    print("8. 🧪 运行功能测试")
    print("0. ❌ 退出")


def get_credentials():
    """获取用户凭据"""
    print("\n🔑 请输入登录信息:")
    username = input("用户名: ").strip()
    if not username:
        print("❌ 用户名不能为空")
        return None, None
    
    password = input("密码: ").strip()
    if not password:
        print("❌ 密码不能为空")
        return None, None
    
    return username, password


def run_test():
    """运行测试"""
    print("\n🧪 运行功能测试...")
    try:
        import test_msec_simple
        return test_msec_simple.main()
    except ImportError:
        print("❌ 测试脚本不存在")
        return False
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        return False


def main():
    """主函数"""
    print_banner()
    
    while True:
        print_menu()
        
        try:
            choice = input("\n请选择 (0-8): ").strip()
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作，再见！")
            sys.exit(0)
        
        if choice == "0":
            print("👋 再见！")
            break
        
        elif choice == "8":
            run_test()
            input("\n按回车键继续...")
            continue
        
        # 需要登录的操作
        if choice in ["1", "2", "3", "4", "5", "6", "7"]:
            username, password = get_credentials()
            if not username or not password:
                continue
            
            print(f"\n🔧 创建客户端...")
            client = MSECSimpleClient(username, password)
            
            if choice == "1":
                # 完整签到流程
                print("\n🚀 开始完整签到流程...")
                success = client.run_full_process()
                if success:
                    print("🎉 签到流程完成！")
                else:
                    print("❌ 签到流程失败")
            
            elif choice == "2":
                # 仅登录测试
                print("\n🔐 开始登录测试...")
                if client.user_login():
                    print("✅ 登录测试成功！")
                else:
                    print("❌ 登录测试失败")
            
            elif choice in ["3", "4", "5", "6", "7"]:
                # 需要先登录的单项操作
                print("\n🔐 正在登录...")
                if not client.user_login():
                    print("❌ 登录失败，无法继续")
                    input("按回车键继续...")
                    continue
                
                if choice == "3":
                    print("\n👤 获取用户信息...")
                    client.get_user_info()
                
                elif choice == "4":
                    print("\n💰 获取积分信息...")
                    client.get_points()
                
                elif choice == "5":
                    print("\n🎭 获取角色信息...")
                    client.get_roles()
                
                elif choice == "6":
                    print("\n📅 获取签到历史...")
                    from msec_simple_checkin import get_week_checkin_params
                    start_date, days = get_week_checkin_params()
                    client.get_checkin_history(start_date, days)
                
                elif choice == "7":
                    print("\n📝 执行签到...")
                    if client.checkin():
                        print("✅ 签到成功！")
                    else:
                        print("❌ 签到失败")
        
        else:
            print("❌ 无效选择，请输入 0-8")
        
        input("\n按回车键继续...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作，再见！")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
