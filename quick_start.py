#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSEC快速启动脚本
提供更友好的用户界面和选项
"""

import sys
from msec_simple_checkin import MSECSimpleClient


def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("           MSEC简化自动签到脚本")
    print("         绿盟科技安全云 - 快速签到")
    print("=" * 60)


def print_menu():
    """打印菜单"""
    print("\n📋 请选择操作:")
    print("1. 🚀 完整签到流程（推荐）")
    print("2. 🔐 仅登录测试")
    print("3. 👤 获取用户信息")
    print("4. 💰 查看积分信息")
    print("5. 🎭 查看角色信息")
    print("6. 📅 查看签到历史")
    print("7. 📝 仅执行签到")
    print("8. 🔑 Token管理")
    print("0. ❌ 退出")


def get_credentials():
    """获取用户凭据"""
    print("\n🔑 请输入登录信息:")
    username = input("用户名: ").strip()
    if not username:
        print("❌ 用户名不能为空")
        return None, None, None, None

    password = input("密码: ").strip()
    if not password:
        print("❌ 密码不能为空")
        return None, None, None, None

    # 验证码识别配置
    print("\n🤖 验证码识别配置:")
    enable_auto = input("是否启用自动验证码识别? (y/N): ").strip().lower() == 'y'

    captcha_token = None
    if enable_auto:
        captcha_token = input("请输入验证码识别API Token: ").strip()
        if not captcha_token:
            print("⚠️ 未提供Token，将禁用自动识别")
            enable_auto = False

    return username, password, captcha_token, enable_auto


def token_management(username: str, password: str):
    """Token管理功能"""
    print("\n🔑 Token管理")
    print("-" * 30)

    client = MSECSimpleClient(username, password, enable_token_storage=True)

    while True:
        print("\n📋 Token管理选项:")
        print("1. 📊 查看Token信息")
        print("2. 🗑️ 删除保存的Token")
        print("3. ✅ 验证Token有效性")
        print("4. 📁 查看Token文件状态")
        print("5. 👥 查看所有用户")
        print("0. 🔙 返回主菜单")

        try:
            choice = input("\n请选择 (0-5): ").strip()
        except KeyboardInterrupt:
            break

        if choice == "0":
            break
        elif choice == "1":
            # 查看Token信息
            print("\n📊 Token信息:")
            token_info = client.get_token_info()
            if token_info:
                import time
                save_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(token_info['save_time']))
                expire_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(token_info['expire_time']))

                print(f"   用户名: {token_info['username']}")
                print(f"   保存时间: {save_time}")
                print(f"   过期时间: {expire_time}")
                print(f"   剩余时间: {token_info['remaining_seconds']}秒")
                print(f"   是否有效: {'✅ 有效' if token_info['is_valid'] else '❌ 已过期'}")
            else:
                print("   ❌ 未找到Token信息")

        elif choice == "2":
            # 删除Token
            print("\n🗑️ 删除Token:")
            confirm = input("   确认删除保存的Token? (y/N): ").strip().lower()
            if confirm == 'y':
                success = client.logout()
                if success:
                    print("   ✅ Token已删除")
                else:
                    print("   ❌ 删除失败")
            else:
                print("   ⚠️ 操作已取消")

        elif choice == "3":
            # 验证Token有效性
            print("\n✅ 验证Token有效性:")
            is_valid = client.is_logged_in()
            print(f"   Token状态: {'✅ 有效' if is_valid else '❌ 无效或已过期'}")

        elif choice == "4":
            # 查看文件状态
            print("\n📁 Token文件状态:")
            file_exists = client.token_storage.file_exists()
            print(f"   文件路径: {client.token_storage.storage_file}")
            print(f"   文件存在: {'✅ 是' if file_exists else '❌ 否'}")
            if file_exists:
                import os
                file_size = os.path.getsize(client.token_storage.storage_file)
                print(f"   文件大小: {file_size} 字节")

        elif choice == "5":
            # 查看所有用户
            print("\n👥 所有用户:")
            all_users = client.token_storage.get_all_users()
            if all_users:
                for i, user in enumerate(all_users, 1):
                    print(f"   {i}. {user}")
                print(f"\n   总计: {len(all_users)} 个用户")
            else:
                print("   ❌ 未找到任何用户")

        else:
            print("❌ 无效选择，请输入 0-5")

        input("\n按回车键继续...")


def main():
    """主函数"""
    print_banner()
    
    while True:
        print_menu()
        
        try:
            choice = input("\n请选择 (0-8): ").strip()
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作，再见！")
            sys.exit(0)

        if choice == "0":
            print("👋 再见！")
            break

        elif choice == "8":
            # Token管理
            username, password, _, _ = get_credentials()
            if username and password:
                token_management(username, password)
            continue

        # 需要登录的操作
        if choice in ["1", "2", "3", "4", "5", "6", "7"]:
            username, password, captcha_token, enable_auto = get_credentials()
            if not username or not password:
                continue

            print(f"\n🔧 创建客户端...")
            client = MSECSimpleClient(
                username,
                password,
                enable_token_storage=True,
                captcha_token=captcha_token,
                enable_auto_captcha=bool(enable_auto)
            )

            if enable_auto:
                print("🤖 已启用自动验证码识别")
            else:
                print("👤 将使用手动验证码输入")
            
            if choice == "1":
                # 完整签到流程
                print("\n🚀 开始完整签到流程...")
                success = client.run_full_process()
                if success:
                    print("🎉 签到流程完成！")
                else:
                    print("❌ 签到流程失败")
            
            elif choice == "2":
                # 仅登录测试
                print("\n🔐 开始登录测试...")
                if client.user_login():
                    print("✅ 登录测试成功！")
                else:
                    print("❌ 登录测试失败")
            
            elif choice in ["3", "4", "5", "6", "7"]:
                # 需要先登录的单项操作
                print("\n🔐 正在登录...")
                if not client.user_login():
                    print("❌ 登录失败，无法继续")
                    input("按回车键继续...")
                    continue
                
                if choice == "3":
                    print("\n👤 获取用户信息...")
                    client.get_user_info()
                
                elif choice == "4":
                    print("\n💰 获取积分信息...")
                    client.get_points()
                
                elif choice == "5":
                    print("\n🎭 获取角色信息...")
                    client.get_roles()
                
                elif choice == "6":
                    print("\n📅 获取签到历史...")
                    from msec_simple_checkin import get_week_checkin_params
                    start_date, days = get_week_checkin_params()
                    client.get_checkin_history(start_date, days)
                
                elif choice == "7":
                    print("\n📝 执行签到...")
                    if client.checkin():
                        print("✅ 签到成功！")
                    else:
                        print("❌ 签到失败")
        
        else:
            print("❌ 无效选择，请输入 0-8")
        
        input("\n按回车键继续...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作，再见！")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
