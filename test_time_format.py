#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间格式化功能
"""

import time
from datetime import datetime


def test_role_expire_time():
    """测试角色过期时间格式化"""
    print("🎭 测试角色过期时间格式化:")
    
    # 模拟角色数据
    test_roles = [
        {"role_name": "普通用户", "expire_time": 0},  # 永久
        {"role_name": "VIP用户", "expire_time": int(time.time()) + 86400 * 30},  # 30天后过期
        {"role_name": "测试用户", "expire_time": 1735689600},  # 2025-01-01
    ]
    
    for role in test_roles:
        role_name = role.get('role_name', '未知角色')
        expire_time = role.get('expire_time', 0)
        
        if expire_time == 0:
            result = f"{role_name}(永久)"
        else:
            try:
                expire_date = datetime.fromtimestamp(expire_time).strftime('%Y-%m-%d')
                result = f"{role_name}(至{expire_date})"
            except:
                result = f"{role_name}(限时)"
        
        print(f"   • {result}")


def test_checkin_time():
    """测试签到时间格式化"""
    print("\n📅 测试签到时间格式化:")
    
    # 模拟签到记录数据
    test_records = [
        {"checkin_time": int(time.time())},  # 当前时间戳
        {"checkin_time": int(time.time()) - 86400},  # 昨天
        {"checkin_time": "2025-07-10 09:30:00"},  # 字符串格式
        {"checkin_time": 1720598400},  # 固定时间戳
    ]
    
    for record in test_records:
        checkin_time = record.get('checkin_time', '')
        if checkin_time:
            try:
                if isinstance(checkin_time, (int, float)):
                    # 如果是时间戳
                    checkin_date = datetime.fromtimestamp(checkin_time).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    # 如果已经是字符串格式
                    checkin_date = str(checkin_time)
                print(f"   • {checkin_date}")
            except Exception as e:
                print(f"   • {checkin_time} (格式化失败: {e})")


def main():
    """主测试函数"""
    print("时间格式化功能测试")
    print("=" * 30)
    
    test_role_expire_time()
    test_checkin_time()
    
    print("\n✅ 时间格式化测试完成")


if __name__ == "__main__":
    main()
