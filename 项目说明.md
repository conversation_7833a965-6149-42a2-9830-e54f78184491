# MSEC简化自动签到脚本

这是一个简化版本的MSEC（绿盟科技安全云）自动签到脚本，专注于核心功能，去除了复杂的数据库管理等功能。

## 📁 项目文件

```
msec_checkin/
├── msec_simple_checkin.py    # 主脚本文件
├── simple_token_storage.py   # Token存储模块
├── quick_start.py            # 交互式启动脚本
├── README_简化版.md          # 详细使用文档
└── 项目说明.md               # 本文件
```

## 🚀 快速开始

### 方式1：直接运行
```bash
python msec_simple_checkin.py
```

### 方式2：交互式菜单
```bash
python quick_start.py
```

## ✨ 主要功能

- ✅ **完整签到流程**：验证码获取、登录、签到
- ✅ **用户信息查询**：获取用户信息、积分、角色
- ✅ **签到历史**：查看本周签到记录
- ✅ **Token存储**：安全的本地Token存储，免重复登录
- ✅ **时间显示**：人性化的时间显示（包含星期）
- ✅ **多用户支持**：支持多个用户独立使用

## 🔐 安全特性

- **加密存储**：使用PBKDF2+Fernet加密算法
- **密码保护**：基于用户密码的加密密钥
- **独立存储**：每个用户独立的Token文件
- **自动过期**：Token过期自动检测和处理

## 📋 依赖要求

```bash
pip install requests cryptography
```

## 💡 使用提示

1. **首次使用**：需要输入验证码进行登录
2. **后续使用**：自动使用保存的Token，无需重复登录
3. **Token管理**：可通过菜单选项管理保存的Token
4. **多用户**：不同用户名会创建独立的Token文件

## 📞 注意事项

- 确保网络能够访问 `https://msec.nsfocus.com`
- 妥善保管账号密码，不要在不安全的环境中使用
- Token文件使用密码加密，忘记密码无法恢复
- 遵守网站使用规则，避免频繁请求

## 🔄 更新记录

- **v1.0**：基础签到功能
- **v1.1**：添加时间显示优化
- **v1.2**：添加Token本地存储功能
- **v1.3**：添加交互式菜单和Token管理

---

**开发说明**：本脚本基于原复杂版本简化而来，专注于核心功能，提供更好的用户体验。
